﻿using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using Avalonia;

namespace RevaloniaAppBuilder;

[Transaction(TransactionMode.Manual)]
[Regeneration(RegenerationOption.Manual)]
public class App : IExternalApplication
{
    public Result OnShutdown(UIControlledApplication application)
    {
        // Unsubscribe from the assembly resolve event
        AppDomain.CurrentDomain.AssemblyResolve -= OnAssemblyResolve;
        // do nothing
        return Result.Succeeded;
    }

    public Result OnStartup(UIControlledApplication application)
    {
        AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;
        
        // Set environment variable to help locate native libraries
        string assemblyLocation = typeof(App).Assembly.Location;
        string basePath = Path.GetDirectoryName(assemblyLocation);
        Environment.SetEnvironmentVariable("PATH", 
            Environment.GetEnvironmentVariable("PATH") + ";" + basePath);
        
        InitAvalonia();
        return Result.Succeeded;
    }
       

    public void InitAvalonia()
    {
        // Load native libraries if needed
        loadNativeLibraries();
        BuildAvaloniaApp().SetupWithoutStarting();
    }
    
    private void loadNativeLibraries()
    {
        var assemblyLocation = typeof(App).Assembly.Location;
        var basePath = Path.GetDirectoryName(assemblyLocation);
    
        // Check various potential locations for native libraries
        var nativePaths = new[]
        {
            Path.Combine(basePath, "runtimes", "win-x64", "native"),
            Path.Combine(basePath, "runtimes", "win", "native"),
            Path.Combine(basePath, "native"),
            basePath
        };
    
        foreach (var nativePath in nativePaths)
        {
            if (Directory.Exists(nativePath))
            {
                // Add this directory to the PATH environment variable
                Environment.SetEnvironmentVariable("PATH", 
                    Environment.GetEnvironmentVariable("PATH") + ";" + nativePath);
            
                // Pre-load native DLLs
                foreach (var file in Directory.GetFiles(nativePath, "*.dll"))
                {
                    try
                    {
                        if (Path.GetFileName(file).StartsWith("libSkiaSharp") || 
                            Path.GetFileName(file).StartsWith("SkiaSharp"))
                        {
                            NativeLibrary.Load(file);
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue
                        System.Diagnostics.Trace.WriteLine($"Failed to load native library {file}: {ex.Message}");
                    }
                }
            }
        }
    }

    private static Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
    {
        Assembly a = null;
        var name = args.Name.Split(',')[0];
        string path = Path.GetDirectoryName(typeof(App).Assembly.Location);

        string assemblyFile = Path.Combine(path, name + ".dll");

        if (File.Exists(assemblyFile))
        {
            a = Assembly.LoadFrom(assemblyFile);
        }

        return a;
    }

    public static AppBuilder BuildAvaloniaApp()
        => AppBuilder
            .Configure<RevaloniaApp>()
            .UsePlatformDetect()
            .LogToTrace()
            .With(new SkiaOptions { MaxGpuResourceSizeBytes = 8096000 });
}